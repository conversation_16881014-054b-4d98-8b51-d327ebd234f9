# PCES Mock Server Documentation

## Overview

The PCES (Patient Care Episode System) Mock Server is a comprehensive implementation that simulates all PCES API endpoints for development, testing, and demonstration purposes. It provides realistic mock responses that match the exact structure and behavior of the real PCES API.

## Features

- ✅ **Complete PCES API Coverage**: All 6 endpoints implemented
- ✅ **HMAC-SHA256 Hash Validation**: Proper security validation
- ✅ **Realistic Mock Data**: Sample players, balances, and transactions
- ✅ **Error Scenarios**: Various error codes and conditions
- ✅ **Request/Response Logging**: Detailed logging for debugging
- ✅ **Configurable Responses**: Different scenarios for testing
- ✅ **Proper HTTP Status Codes**: Match real PCES API behavior

## Supported Endpoints

### 1. Authentication (`POST /auth`)
- **Purpose**: Get player balance and validate session
- **Request**: `{ customer, token }`
- **Response**: `{ balance, bonusBalance, code, currency, status, traderId }`

### 2. Debit (`POST /debit`)
- **Purpose**: Place bet (debit player balance)
- **Request**: `{ customer, token, gameId, amount, currency, betId, trxId, tip? }`
- **Response**: `{ balance, bonusBalance, code, currency, status, trxId }`

### 3. Credit (`POST /credit`)
- **Purpose**: Settle win (credit player balance)
- **Request**: `{ customer, token, gameId, amount, currency, betId, trxId, freespin? }`
- **Response**: `{ balance, bonusBalance, code, currency, status, trxId? }`

### 4. Debit-Credit (`POST /debit-credit`)
- **Purpose**: Combined bet and settlement
- **Request**: `{ customer, token, gameId, amount, creditAmount, currency, betId, trxId, creditTrxId, tip? }`
- **Response**: `{ balance, bonusBalance, code, currency, status, trxId, creditTrxId? }`

### 5. Rollback (`POST /rollback`)
- **Purpose**: Reverse transaction
- **Request**: `{ customer, token, gameId, trxId }`
- **Response**: `{ balance, bonusBalance, code, currency, status, trxId }`

### 6. Promo (`POST /promo`)
- **Purpose**: Promotional wins (freespins, jackpots, etc.)
- **Request**: `{ customer, token, gameId, amount, currency, betId, trxId, promo }`
- **Response**: `{ balance, bonusBalance, code, currency, status, trxId }`

## Quick Start

### 1. Start the Mock Server

```bash
# Development mode
npm run start:mock

# Production mode
npm run build
npm run start:mock:prod
```

The server will start on `http://localhost:3001` by default.

### 2. Test Basic Connectivity

```bash
# Health check
curl http://localhost:3001/health

# Version info
curl http://localhost:3001/version
```

### 3. Test Authentication

```bash
curl -X POST http://localhost:3001/auth \
  -H "Content-Type: application/json" \
  -H "Generic-Id: test_generic_id" \
  -H "Hash: CALCULATED_HASH" \
  -d '{
    "customer": "*************",
    "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344"
  }'
```

## Test Players

The mock server comes with pre-configured test players:

### Player 1 (Turkish Lira)
- **Customer**: `*************`
- **Token**: `692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344`
- **Balance**: 120.548 TRY
- **Bonus Balance**: 5.1 TRY
- **Trader ID**: 1

### Player 2 (Euro)
- **Customer**: `2019045569026`
- **Token**: `TEST_TOKEN_EUR_PLAYER`
- **Balance**: 250.75 EUR
- **Bonus Balance**: 10.25 EUR
- **Trader ID**: 2

### Player 3 (US Dollar)
- **Customer**: `2020123456789`
- **Token**: `TEST_TOKEN_USD_PLAYER`
- **Balance**: 1000.00 USD
- **Bonus Balance**: 50.00 USD
- **Trader ID**: 3

### Player 4 (Suspended)
- **Customer**: `SUSPENDED_PLAYER`
- **Token**: `SUSPENDED_TOKEN`
- **Balance**: 0.00 EUR
- **Bonus Balance**: 0.00 EUR
- **Status**: Inactive (for testing error scenarios)

## Configuration

### Environment Variables

```bash
# Server configuration
MOCK_PORT=3001
MOCK_HOST=0.0.0.0
INTERNAL_SERVER_PORT=4006

# Mock behavior
MOCK_LOGGING=true
SKIP_HASH_VALIDATION=false
NODE_ENV=development

# Security
MOCK_SECRET_KEY=mock_secret_key_for_testing
```

### Hash Validation

The mock server validates HMAC-SHA256 hashes for all requests. To skip validation during development:

```bash
export SKIP_HASH_VALIDATION=true
export NODE_ENV=development
```

## Testing Scenarios

### Successful Operations

```bash
# 1. Authentication
curl -X POST http://localhost:3001/auth \
  -H "Content-Type: application/json" \
  -H "Generic-Id: test_id" \
  -H "Hash: VALID_HASH" \
  -d '{"customer": "*************", "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344"}'

# 2. Place bet
curl -X POST http://localhost:3001/debit \
  -H "Content-Type: application/json" \
  -H "Generic-Id: test_id" \
  -H "Hash: VALID_HASH" \
  -d '{
    "customer": "*************",
    "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344",
    "gameId": "test_game",
    "amount": 10.0,
    "currency": "TRY",
    "betId": "bet_123",
    "trxId": "trx_456"
  }'

# 3. Settle win
curl -X POST http://localhost:3001/credit \
  -H "Content-Type: application/json" \
  -H "Generic-Id: test_id" \
  -H "Hash: VALID_HASH" \
  -d '{
    "customer": "*************",
    "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344",
    "gameId": "test_game",
    "amount": 25.0,
    "currency": "TRY",
    "betId": "bet_123",
    "trxId": "trx_789"
  }'
```

### Error Scenarios

```bash
# Invalid customer
curl -X POST http://localhost:3001/auth \
  -H "Content-Type: application/json" \
  -H "Generic-Id: test_id" \
  -H "Hash: VALID_HASH" \
  -d '{"customer": "INVALID_CUSTOMER", "token": "INVALID_TOKEN"}'
# Response: {"code": 22, "status": "CUSTOMER_NOT_FOUND"}

# Insufficient funds
curl -X POST http://localhost:3001/debit \
  -H "Content-Type: application/json" \
  -H "Generic-Id: test_id" \
  -H "Hash: VALID_HASH" \
  -d '{
    "customer": "*************",
    "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344",
    "gameId": "test_game",
    "amount": 10000.0,
    "currency": "TRY",
    "betId": "bet_large",
    "trxId": "trx_large"
  }'
# Response: {"code": 24, "status": "INSUFFICIENT_FUNDS"}
```

## Error Codes

The mock server supports all PCES error codes:

| Code | Status | Description |
|------|--------|-------------|
| 0 | SUCCESS | Operation successful |
| 1 | UNKNOWN_ERROR | Unknown error occurred |
| 4 | UNAUTHORIZED_REQUEST | Invalid hash or authorization |
| 9 | BET_RECORD_NOT_FOUND | Bet record not found |
| 10 | TRANSACTION_NOT_FOUND | Transaction not found |
| 11 | BET_ALREADY_WON | Bet already won |
| 12 | BET_ALREADY_SETTLED | Bet already settled |
| 22 | CUSTOMER_NOT_FOUND | Customer not found |
| 24 | INSUFFICIENT_FUNDS | Insufficient funds |
| 25 | PLAYER_SUSPENDED | Player account suspended |
| 27 | TOKEN_NOT_FOUND | Token not found |
| 29 | TOKEN_INVALID | Invalid token |

## Development Features

### Request/Response Logging

All requests and responses are logged for debugging:

```
[PCES MOCK] POST /auth - Customer: *************
[AUTH RESPONDER] Processing auth request for customer: *************
[HASH VALIDATOR] Hash validation successful
[AUTH RESPONDER] Auth successful for customer: *************, Balance: 120.548
```

### Mock Data Management

The mock server maintains in-memory data for:
- Player balances and information
- Transaction history
- Bet records
- Token validation

### Hash Calculation Helper

For testing, you can calculate valid hashes using the built-in utility:

```javascript
// In your test code
const hashValidator = new HashValidatorService();
const validHash = hashValidator.generateValidHash(requestBody, genericId);
```

## Integration with Real Systems

### Switching Between Mock and Real API

Use environment variables to switch between mock and real PCES API:

```bash
# Use mock server
export USE_MOCK_PCES=true
export MOCK_PCES_URL=http://localhost:3001

# Use real PCES API
export USE_MOCK_PCES=false
export PCES_API_URL=https://real-pces-api.com
```

### Docker Deployment

```dockerfile
# Dockerfile for mock server
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "run", "start:mock"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  pces-mock:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - MOCK_PORT=3001
      - SKIP_HASH_VALIDATION=true
```

## Troubleshooting

### Common Issues

1. **Hash Validation Errors**
   - Set `SKIP_HASH_VALIDATION=true` for development
   - Ensure correct hash calculation algorithm
   - Check Generic-Id header is present

2. **Customer Not Found**
   - Use one of the pre-configured test customers
   - Check customer ID format and spelling

3. **Token Invalid**
   - Use the correct token for each test customer
   - Ensure token hasn't expired (mock tokens don't expire)

4. **Port Already in Use**
   - Change `MOCK_PORT` environment variable
   - Kill existing processes on port 3001

### Debug Mode

Enable detailed logging:

```bash
export MOCK_LOGGING=true
export DEBUG=pces:*
npm run start:mock
```

## API Testing Tools

### Postman Collection

Import the provided Postman collection for easy testing:
- All endpoints pre-configured
- Valid test data included
- Error scenarios covered

### curl Examples

See the testing scenarios section above for comprehensive curl examples.

## Files Created

The PCES Mock Server implementation includes the following files:

### Core Implementation
- `src/mainMock.ts` - Main entry point for the mock server
- `src/mock/mock.module.ts` - NestJS module configuration
- `src/mock/controllers/pces.controller.ts` - PCES API controller
- `src/mock/services/pces.service.ts` - Business logic service

### Data and Utilities
- `src/mock/data/mock-data.service.ts` - Mock data management
- `src/mock/utils/hash-validator.ts` - HMAC-SHA256 validation
- `src/mock/utils/response-builder.ts` - Response formatting

### Endpoint Responders
- `src/mock/responders/auth.responder.ts` - Authentication endpoint
- `src/mock/responders/debit.responder.ts` - Debit endpoint
- `src/mock/responders/credit.responder.ts` - Credit endpoint
- `src/mock/responders/debit-credit.responder.ts` - Debit-credit endpoint
- `src/mock/responders/rollback.responder.ts` - Rollback endpoint
- `src/mock/responders/promo.responder.ts` - Promo endpoint

### Error Handling and Middleware
- `src/mock/filters/pces-error.filter.ts` - PCES-specific error handling
- `src/mock/middleware/hash-validation.middleware.ts` - Hash validation middleware

### Configuration and Deployment
- `.env.mock` - Environment configuration template
- `start-mock-server.sh` - Startup script
- `Dockerfile.mock` - Docker configuration
- `docker-compose.mock.yml` - Docker Compose setup

### Testing and Documentation
- `test-mock-server.js` - Basic functionality tests
- `test-pces-scenarios.js` - Comprehensive test suite
- `postman/PCES_Mock_Server.postman_collection.json` - Postman collection
- `docs/PCES_MOCK_SERVER.md` - This documentation

## Quick Start Commands

```bash
# Method 1: Using the startup script
./start-mock-server.sh

# Method 2: Using npm script
npm run start:mock

# Method 3: Using Docker
docker-compose -f docker-compose.mock.yml up

# Method 4: Direct execution
npx ts-node src/mainMock.ts
```

## Testing Commands

```bash
# Basic functionality test
node test-mock-server.js

# Comprehensive test suite
node test-pces-scenarios.js

# Health check
curl http://localhost:3001/health
```

## Support

For issues or questions about the PCES Mock Server:
1. Check the logs for detailed error information
2. Verify your request format matches the examples
3. Ensure all required headers are present
4. Test with the provided sample data first
5. Use the comprehensive test suite to verify functionality
