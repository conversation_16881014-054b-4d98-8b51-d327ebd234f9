# SW Integration Service Templates

This document provides service layer templates for implementing integration systems.

## Service Templates

### 1. Payment Service (src/wallet/payment/payment.service.ts)

```typescript
import { Injectable } from "@nestjs/common";
import {
    BalanceSupport,
    BonusPaymentSupport,
    CommitPaymentRequest,
    HttpGateway,
    JackpotPaymentSupport,
    SplitPaymentSupport
} from "@skywind-group/sw-integration-core";
import { Balance, Balances, RequireRefundBetError, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { RoundService } from "@wallet/round/round.service";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { logging, measures } from "@skywind-group/sw-utils";

const log = logging.logger("PaymentService");
const { measure } = measures;

@Injectable()
export class PaymentService implements 
    BalanceSupport, 
    SplitPaymentSupport, 
    JackpotPaymentSupport, 
    BonusPaymentSupport {
    
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly roundService: RoundService,
        private readonly betHandler: BetHttpHandler,
        private readonly winHandler: WinHttpHandler,
        private readonly balanceHandler: BalanceHttpHandler
    ) {}

    @measure({ name: "PaymentService.getBalance", isAsync: true })
    public async getBalance(req: IntegrationPaymentRequest): Promise<Balance> {
        return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
            req,
            this.balanceHandler
        );
    }

    @measure({ name: "PaymentService.commitBetPayment", isAsync: true })
    public async commitBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            if (!req.request.bet) {
                log.info("Fetch balance instead of zero-bet");
                return this.getBalance(req);
            }
            
            const operatorRoundId = await this.openAndGetRoundIfNeeded(req);

            return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId },
                this.betHandler
            );
        } catch (err) {
            if (this.isRefundNeeded(req, err)) {
                log.info(err, "Refund operator's bet");
                throw new RequireRefundBetError();
            }

            log.info(err, "Rollback bet");
            throw err;
        }
    }

    @measure({ name: "PaymentService.commitWinPayment", isAsync: true })
    public async commitWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        const operatorRoundId = await this.getOperatorRoundId(req);
        let balance: Balance;

        if (this.isWinCommittable(req)) {
            try {
                balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                    { ...req, operatorRoundId },
                    this.winHandler
                );
            } catch (err) {
                if (this.isRoundAlreadyClosed(err) && req.request.retry) {
                    log.warn(err, "Round is already closed. Fetch balance instead");
                    return this.getBalance(req);
                }
                throw err;
            }
        } else {
            log.info("Fetch balance instead of zero-win");
            balance = await this.getBalance(req);
        }

        return balance;
    }

    @measure({ name: "PaymentService.commitJackpotWinPayment", isAsync: true })
    public async commitJackpotWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // Implement jackpot win logic
        return this.commitWinPayment(req);
    }

    @measure({ name: "PaymentService.commitBonusPayment", isAsync: true })
    public async commitBonusPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // Implement bonus payment logic
        return this.commitWinPayment(req);
    }

    public async getBalances(req: IntegrationPaymentRequest): Promise<Balances> {
        const balance = await this.getBalance(req);
        return {
            totalBalance: balance,
            cashBalance: {
                totalAmount: balance.cashAmount || 0,
                currency: balance.currency
            },
            bonusBalance: {
                totalAmount: balance.bonusAmount || 0,
                currency: balance.currency
            }
        };
    }

    private async openAndGetRoundIfNeeded(req: IntegrationPaymentRequest): Promise<string> {
        const existingRound = await this.roundService.findBySwRoundId(req.request.roundPID);
        if (existingRound) {
            return existingRound.operatorRoundId;
        }

        return await this.roundService.openRound(req);
    }

    private async getOperatorRoundId(req: IntegrationPaymentRequest): Promise<string> {
        const round = await this.roundService.findBySwRoundId(req.request.roundPID);
        if (!round) {
            throw new Error(`Round not found: ${req.request.roundPID}`);
        }
        return round.operatorRoundId;
    }

    private isWinCommittable(req: IntegrationPaymentRequest): boolean {
        return req.request.totalWin > 0;
    }

    private isRefundNeeded(req: IntegrationPaymentRequest, err: SWError): boolean {
        // Implement refund logic based on error type
        return false;
    }

    private isRoundAlreadyClosed(err: SWError): boolean {
        // Implement round closed detection logic
        return false;
    }
}
```

### 2. Round Service (src/wallet/round/round.service.ts)

```typescript
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { RoundEntity } from "@wallet/round/round.entity";
import { IntegrationPaymentRequest } from "@entities/integration.entities";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import { StartRoundHttpHandler, EndRoundHttpHandler } from "@wallet/payment/round.http.handler";
import { logging, measures } from "@skywind-group/sw-utils";
import { generateUniqueId } from "@utils/common";

const log = logging.logger("RoundService");
const { measure } = measures;

@Injectable()
export class RoundService {
    constructor(
        @InjectRepository(RoundEntity)
        private readonly roundRepository: Repository<RoundEntity>,
        private readonly httpGateway: HttpGateway,
        private readonly startRoundHandler: StartRoundHttpHandler,
        private readonly endRoundHandler: EndRoundHttpHandler
    ) {}

    @measure({ name: "RoundService.openRound", isAsync: true })
    public async openRound(req: IntegrationPaymentRequest): Promise<string> {
        const operatorRoundId = generateUniqueId();
        
        // Start round with operator
        const startRoundResponse = await this.httpGateway.request(
            { ...req, operatorRoundId },
            this.startRoundHandler
        );

        // Save round to database
        const round = this.roundRepository.create({
            operatorRoundId: startRoundResponse.GameRoundID || operatorRoundId,
            swRoundId: req.request.roundPID
        });

        await this.roundRepository.save(round);
        
        log.info(`Round opened: SW=${req.request.roundPID}, Operator=${round.operatorRoundId}`);
        
        return round.operatorRoundId;
    }

    @measure({ name: "RoundService.closeRound", isAsync: true })
    public async closeRound(req: IntegrationPaymentRequest): Promise<void> {
        const round = await this.findBySwRoundId(req.request.roundPID);
        if (!round) {
            log.warn(`Round not found for closing: ${req.request.roundPID}`);
            return;
        }

        try {
            await this.httpGateway.request(
                { ...req, operatorRoundId: round.operatorRoundId },
                this.endRoundHandler
            );
            
            log.info(`Round closed: SW=${req.request.roundPID}, Operator=${round.operatorRoundId}`);
        } catch (err) {
            log.error(err, `Failed to close round: ${req.request.roundPID}`);
            throw err;
        }
    }

    public async findBySwRoundId(swRoundId: string): Promise<RoundEntity | undefined> {
        return await this.roundRepository.findOne({
            where: { swRoundId }
        });
    }

    public async findByOperatorRoundId(operatorRoundId: string): Promise<RoundEntity | undefined> {
        return await this.roundRepository.findOne({
            where: { operatorRoundId }
        });
    }

    public async updateFreeSpinToken(swRoundId: string, freeSpinToken: string): Promise<void> {
        await this.roundRepository.update(
            { swRoundId },
            { freeSpinToken }
        );
    }

    public async getFreeSpinToken(swRoundId: string): Promise<string | undefined> {
        const round = await this.findBySwRoundId(swRoundId);
        return round?.freeSpinToken;
    }
}
```

### 3. Launcher Service (src/launcher/launcher.service.ts)

```typescript
import { Injectable, Inject } from "@nestjs/common";
import { BaseHttpService, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { IntegrationGameLaunchRequest, IntegrationStartGameTokenData } from "@entities/integration.entities";
import { OperatorJurisdiction } from "@entities/operator.entities";
import { ValidationError } from "@errors/sw.errors";
import { Names } from "@names";
import { logging, measures } from "@skywind-group/sw-utils";
import * as jwt from "jsonwebtoken";

const log = logging.logger("LauncherService");
const { measure } = measures;

@Injectable()
export class LauncherService {
    public static FALCON_GAME_URL = "v1/merchants/game/url";

    constructor(@Inject(Names.BaseHttpService) private baseHttpService: BaseHttpService) {}

    @measure({ name: "LauncherService.getGameUrl", isAsync: true })
    async getGameUrl(data: IntegrationGameLaunchRequest, ip: string): Promise<string> {
        this.validateRequest(data);
        
        const sanitizedData = this.sanitizeData(data);
        const options = this.mapOperatorToSW(sanitizedData, ip);
        
        const { token, url } = await this.baseHttpService.post<any>(
            LauncherService.FALCON_GAME_URL, 
            options
        );
        
        if (token) {
            const startGameToken: IntegrationStartGameTokenData = this.parseToken(token);
            if (startGameToken.loginFailed) {
                return startGameToken.relaunchUrl;
            }
        }

        return url;
    }

    private validateRequest(data: IntegrationGameLaunchRequest): void {
        if (data.playmode === PlayMode.REAL && !data.PublicToken) {
            throw new ValidationError("PublicToken is required for real mode");
        }
        
        if (data.playmode === PlayMode.FUN && data.jurisdiction !== OperatorJurisdiction.UK) {
            throw new ValidationError(`Fun mode is not supported for ${data.jurisdiction}`);
        }
    }

    private sanitizeData(data: IntegrationGameLaunchRequest): IntegrationGameLaunchRequest {
        return {
            ...data,
            gameCode: data.gameCode.trim(),
            language: data.language.toLowerCase(),
            currency: data.currency.toUpperCase()
        };
    }

    private mapOperatorToSW(data: IntegrationGameLaunchRequest, ip: string): any {
        return {
            gameCode: data.gameCode,
            playMode: data.playmode,
            publicToken: data.PublicToken,
            language: data.language,
            currency: data.currency,
            jurisdiction: data.jurisdiction,
            platform: data.platform,
            playerIp: ip,
            historyUrl: data.historyUrl,
            lobbyUrl: data.lobbyUrl,
            cashierUrl: data.cashierUrl,
            merchantCode: data.merchantCode,
            merchantType: data.merchantType
        };
    }

    private parseToken(token: string): IntegrationStartGameTokenData {
        try {
            return jwt.decode(token) as IntegrationStartGameTokenData;
        } catch (err) {
            log.error(err, "Failed to parse game token");
            throw new ValidationError("Invalid game token");
        }
    }
}
```

## Module Templates

### 1. Wallet Module (src/wallet/wallet.module.ts)

```typescript
import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { PaymentModule, StartGameModule } from "@skywind-group/sw-integration-core";
import config from "@config";
import { PaymentService } from "@wallet/payment/payment.service";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { StartRoundHttpHandler, EndRoundHttpHandler } from "@wallet/payment/round.http.handler";
import { GameTokenService } from "@wallet/start/gameToken.service";
import { GameUrlService } from "@wallet/start/gameUrl.service";
import { RoundService } from "@wallet/round/round.service";
import { RoundRepository } from "@wallet/round/round.repository";
import { DbModule } from "@db/db.module";

@Module({
    providers: [
        GameTokenService,
        GameUrlService,
        RoundService,
        BetHttpHandler,
        WinHttpHandler,
        EndRoundHttpHandler,
        StartRoundHttpHandler,
        BalanceHttpHandler,
        PaymentService
    ],
    exports: [
        GameTokenService,
        GameUrlService,
        RoundService,
        BetHttpHandler,
        WinHttpHandler,
        EndRoundHttpHandler,
        StartRoundHttpHandler,
        BalanceHttpHandler,
        PaymentService
    ],
    imports: [
        StartGameModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                createGameToken: GameTokenService,
                createGameURL: GameUrlService
            },
            [WalletModule]
        ),
        PaymentModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                payment: PaymentService,
                jackpotPayment: PaymentService,
                bonusPayment: PaymentService
            },
            [WalletModule]
        ),
        TypeOrmModule.forFeature([RoundRepository]),
        DbModule
    ]
})
export class WalletModule {}
```

### 2. Launcher Module (src/launcher/launcher.module.ts)

```typescript
import { Module } from "@nestjs/common";
import { LauncherController } from "./launcher.controller";
import { LauncherService } from "./launcher.service";
import { BaseHttpService } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { Names } from "@names";

@Module({
    controllers: [LauncherController],
    providers: [
        LauncherService,
        {
            provide: Names.BaseHttpService,
            useFactory: () => {
                return new BaseHttpService({
                    baseURL: config.swOperatorUrl,
                    timeout: 10000
                });
            }
        }
    ]
})
export class LauncherModule {}
```

### 3. Operator Module (src/operator/modules/operator.module.ts)

```typescript
import { Module } from "@nestjs/common";
import { HistoryController } from "@operator/controllers/history.controller";
import { HistoryService } from "@operator/services/history.service";
import { PlayCheckerHttpHandler } from "@operator/handlers/playChecker.http.handler";
import { InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import config from "@config";
import { Names } from "@names";

@Module({
    controllers: [HistoryController],
    providers: [
        HistoryService,
        PlayCheckerHttpHandler,
        {
            provide: Names.InternalAPIService,
            useFactory: () => {
                return new InternalAPIService({
                    baseURL: config.swInternalMapiUrl,
                    timeout: 10000
                });
            }
        },
        {
            provide: HttpGateway,
            useFactory: () => {
                return new HttpGateway(config.http);
            }
        }
    ]
})
export class OperatorModule {}
```

### 4. Database Module (src/db/db.module.ts)

```typescript
import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import config from "@config";
import { DbLogger } from "@utils/db-logger";
import { RoundEntity } from "@wallet/round/round.entity";

@Module({
    imports: [
        TypeOrmModule.forRoot({
            type: "postgres",
            host: config.db.host,
            port: config.db.port,
            username: config.db.user,
            password: config.db.password,
            database: config.db.database,
            entities: [RoundEntity],
            synchronize: config.db.syncOnStart,
            logging: true,
            logger: config.db.logEnabled && new DbLogger(),
            cache: config.db.cache.isEnabled && { duration: config.db.cache.ttl },
            connectTimeoutMS: config.db.connectionTimeoutInMs,
            ssl: config.db.ssl.isEnabled ? {
                ca: config.db.ssl.ca
            } : false
        })
    ]
})
export class DbModule {}
```

## Controller Templates

### 1. Launcher Controller (src/launcher/launcher.controller.ts)

```typescript
import { Controller, Get, Query, UseFilters, Redirect, HttpStatus } from "@nestjs/common";
import { ValidationPipe } from "@nestjs/common";
import { LauncherService } from "./launcher.service";
import { IntegrationGameLaunchRequest } from "@entities/integration.entities";
import { BaseErrorsFilter, ClientIp } from "@skywind-group/sw-integration-core";

@Controller("game")
export class LauncherController {
    constructor(private launcherService: LauncherService) {}

    @Get("/url")
    @UseFilters(BaseErrorsFilter)
    @Redirect(undefined, HttpStatus.FOUND)
    async getGameUrl(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }

    @Get("/url/noredirect")
    @UseFilters(BaseErrorsFilter)
    async getGameUrlWithoutRedirection(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }
}
```

### 2. History Controller (src/operator/controllers/history.controller.ts)

```typescript
import { Controller, Get, Query, UseFilters } from "@nestjs/common";
import { ValidationPipe } from "@nestjs/common";
import { HistoryService } from "@operator/services/history.service";
import { OperatorHistoryRequest } from "@entities/operator.entities";
import { BaseErrorsFilter } from "@skywind-group/sw-integration-core";

@Controller("history")
export class HistoryController {
    constructor(private historyService: HistoryService) {}

    @Get("/image")
    @UseFilters(BaseErrorsFilter)
    async getGameHistoryImageUrl(
        @Query(new ValidationPipe()) request: OperatorHistoryRequest
    ): Promise<{ imageUrl: string }> {
        const imageUrl = await this.historyService.getGameHistoryImageUrl(request);
        return { imageUrl };
    }
}
```

## Utility Templates

### 1. Common Utilities (src/utils/common.ts)

```typescript
import config from "@config";

export function generateNumber(length: number): number {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function generateUniqueId(): string {
    return `${Date.now()}_${generateNumber(6)}`;
}

export function sumMajorUnits(...amounts: number[]): number {
    const total = amounts.reduce((sum, amount) => sum + (amount || 0), 0);
    return Math.round(total * config.currencyUnitMultiplier);
}

export function sumMajorUnitsInArray(amounts: number[]): number {
    return sumMajorUnits(...amounts);
}

export function buildMerchantCode(jurisdiction: string, merchantCode: string): string {
    return `${jurisdiction}_${merchantCode}`;
}

export function createDateStringWithOperatorFormat(timestamp: number): string {
    return new Date(timestamp).toISOString();
}
```

### 2. Database Logger (src/utils/db-logger.ts)

```typescript
import { Logger } from "typeorm";
import { logging } from "@skywind-group/sw-utils";

export class DbLogger implements Logger {
    private logger = logging.logger("TypeORM");

    logQuery(query: string, parameters?: any[]): void {
        this.logger.debug({ query, parameters }, "SQL Query");
    }

    logQueryError(error: string, query: string, parameters?: any[]): void {
        this.logger.error({ error, query, parameters }, "SQL Query Error");
    }

    logQuerySlow(time: number, query: string, parameters?: any[]): void {
        this.logger.warn({ time, query, parameters }, "Slow SQL Query");
    }

    logSchemaBuild(message: string): void {
        this.logger.info(message, "Schema Build");
    }

    logMigration(message: string): void {
        this.logger.info(message, "Migration");
    }

    log(level: "log" | "info" | "warn", message: any): void {
        this.logger[level === "log" ? "info" : level](message);
    }
}
```

### 3. Client IP Decorator (src/utils/clientIp.decorator.ts)

```typescript
import { createParamDecorator, ExecutionContext } from "@nestjs/common";

export const ClientIp = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): string => {
        const request = ctx.switchToHttp().getRequest();
        return request.ip ||
               request.connection.remoteAddress ||
               request.socket.remoteAddress ||
               (request.connection.socket ? request.connection.socket.remoteAddress : null) ||
               "127.0.0.1";
    }
);
```

## Error Templates

### 1. Operator Error Mapping (src/errors/operator.errors.ts)

```typescript
import { OperatorError } from "@entities/operator.entities";
import {
    SWError,
    InsufficientBalanceError,
    AuthenticateFailedError,
    GeneralError,
    TransactionNotFound,
    ValidationError
} from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";

export const operatorErrorCodes = {
    TechnicalError: 1000,
    AuthenticationFailed: 1001,
    InsufficientFunds: 1002,
    TransactionNotFound: 1003,
    InvalidAmount: 1004,
    InvalidTransactionType: 1005,
    GamingLimits: 1006,
    PlayerSuspended: 1007,
    InvalidGameRound: 1008
};

export function mapOperatorToSWError(
    { ErrorCode, ErrorMessage }: OperatorError,
    response?: superagent.Response
): SWError {
    switch (ErrorCode) {
        case operatorErrorCodes.TechnicalError:
            return new GeneralError(ErrorMessage);
        case operatorErrorCodes.AuthenticationFailed:
            return new AuthenticateFailedError(ErrorMessage);
        case operatorErrorCodes.InsufficientFunds:
            return new InsufficientBalanceError(ErrorMessage);
        case operatorErrorCodes.TransactionNotFound:
            return new TransactionNotFound(ErrorMessage);
        case operatorErrorCodes.InvalidAmount:
        case operatorErrorCodes.InvalidTransactionType:
            return new ValidationError(ErrorMessage);
        case operatorErrorCodes.GamingLimits:
        case operatorErrorCodes.PlayerSuspended:
            return new ValidationError(ErrorMessage);
        case operatorErrorCodes.InvalidGameRound:
            return new ValidationError(ErrorMessage);
        default:
            return new GeneralError(ErrorMessage || "Unknown operator error");
    }
}

// Predefined operator errors for testing
export const operatorInsufficientFunds = (): OperatorError => ({
    ErrorCode: operatorErrorCodes.InsufficientFunds,
    ErrorMessage: "Insufficient funds"
});

export const operatorInvalidAmount = (): OperatorError => ({
    ErrorCode: operatorErrorCodes.InvalidAmount,
    ErrorMessage: "Invalid amount"
});

export const operatorAuthenticationFailed = (): OperatorError => ({
    ErrorCode: operatorErrorCodes.AuthenticationFailed,
    ErrorMessage: "Authentication failed"
});
```

This completes the comprehensive service and implementation templates for building SW integration systems. These templates provide everything needed to implement a robust, scalable integration following the established architectural patterns.
