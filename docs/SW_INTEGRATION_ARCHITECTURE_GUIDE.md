# SW Integration System Architecture Guide

## Overview

This guide provides comprehensive documentation for building integration systems following established patterns. The system follows a modular, microservices-based architecture using NestJS framework with TypeScript.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Implementation Patterns](#implementation-patterns)
5. [Configuration Management](#configuration-management)
6. [Error Handling](#error-handling)
7. [Testing Strategy](#testing-strategy)
8. [Database Integration](#database-integration)
9. [HTTP Communication](#http-communication)
10. [Deployment & Scaling](#deployment--scaling)

## Architecture Overview

### Multi-Service Architecture

The system is designed as a collection of specialized microservices:

- **Wallet Service** (`mainWallet.ts`) - Handles payment operations, balance management, and financial transactions
- **Launcher Service** (`mainLauncher.ts`) - Manages game URL generation and game launching
- **Operator Service** (`mainOperator.ts`) - Handles operator-specific integrations and communications
- **Mock Service** (`mainMock.ts`) - Provides testing and development mock implementations

### Key Architectural Principles

1. **Separation of Concerns**: Each service has a specific responsibility
2. **Modular Design**: Functionality is organized into reusable modules
3. **Dependency Injection**: Uses NestJS DI container for loose coupling
4. **Configuration-Driven**: Environment-based configuration management
5. **Error Resilience**: Comprehensive error handling and retry mechanisms

## Project Structure

```
src/
├── bootstrap/           # Application bootstrapping logic
├── config.ts           # Central configuration management
├── db/                 # Database configuration and modules
├── entities/           # Data models and DTOs
├── errors/             # Custom error definitions
├── launcher/           # Game launcher service
├── mock/               # Mock implementations for testing
├── operator/           # Operator integration service
├── payment/            # Payment processing modules
├── utils/              # Shared utilities and helpers
├── wallet/             # Wallet service implementation
├── mainWallet.ts       # Wallet service entry point
├── mainLauncher.ts     # Launcher service entry point
├── mainOperator.ts     # Operator service entry point
└── mainMock.ts         # Mock service entry point
```

### Module Organization

Each service follows a consistent module structure:

```
service/
├── service.module.ts   # Module definition and DI configuration
├── service.controller.ts # HTTP endpoints and request handling
├── service.service.ts  # Business logic implementation
├── handlers/           # HTTP request handlers
├── entities/           # Service-specific entities
└── *.spec.ts          # Unit tests
```

## Core Components

### 1. Bootstrap System

The bootstrap system provides a unified way to start services:

```typescript
// Example from mainWallet.ts
import { bootstrapServer } from "@skywind-group/sw-integration-core";

bootstrapServer({
    serviceName: "sw-wallet",
    versionFile: "./lib/version",
    module: WalletModule,
    internalPort: config.internalServer.port,
    port: config.server.walletPort,
    secureKeys: config.securedKeys
});
```

### 2. HTTP Handlers Pattern

HTTP handlers encapsulate request/response logic:

```typescript
export class WinHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const winRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            Amount: sumMajorUnits(req.request.totalWin, -req.request.totalJpWin || 0),
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.WIN,
            GameRoundID: req.operatorRoundId
        };
        
        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: winRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        });
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        return this.parseHttpResponse<OperatorTransactionResponse>(response);
    }
}
```

### 3. Service Layer Pattern

Services contain business logic and orchestrate handlers:

```typescript
@Injectable()
export class PaymentService implements 
    BalanceSupport, 
    SplitPaymentSupport, 
    JackpotPaymentSupport, 
    BonusPaymentSupport {
    
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly roundService: RoundService,
        private readonly betHandler: BetHttpHandler,
        private readonly winHandler: WinHttpHandler
    ) {}

    public async commitBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            if (!req.request.bet) {
                return this.getBalance(req);
            }
            const operatorRoundId = await this.openAndGetRoundIfNeeded(req);
            
            return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId },
                this.betHandler
            );
        } catch (err) {
            if (this.isRefundNeeded(req, err)) {
                throw new RequireRefundBetError();
            }
            throw err;
        }
    }
}
```

## Implementation Patterns

### 1. Entity Design

Entities use composition and inheritance for flexibility:

```typescript
// Integration entities extend base entities
export interface IntegrationGameTokenData extends MerchantGameTokenData, OperatorGameTokenData {}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
    actionType?: OperatorActionType;
}

// Validation using class-validator
export class IntegrationGameLaunchRequest {
    @IsDefined()
    @IsNotEmpty()
    gameCode: string;

    @IsDefined()
    @IsIn([PlayMode.REAL, PlayMode.FUN])
    playmode: PlayMode;

    @IsOptional()
    @IsNotEmpty()
    PublicToken?: string;
}
```

### 2. Module Configuration

Modules use dynamic configuration for flexibility:

```typescript
@Module({
    providers: [
        GameTokenService,
        PaymentService,
        // ... other providers
    ],
    imports: [
        StartGameModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                createGameToken: GameTokenService,
                createGameURL: GameUrlService,
                keepAlive: TransferService
            },
            [WalletModule]
        ),
        PaymentModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                payment: PaymentService,
                jackpotPayment: PaymentService,
                refund: RefundService,
                regulation: RegulationService
            },
            [WalletModule]
        )
    ]
})
export class WalletModule {}
```

### 3. Error Mapping Pattern

Systematic error mapping between operator and system errors:

```typescript
export function mapOperatorToSWError(
    { ErrorCode, ErrorMessage }: OperatorError,
    response?: superagent.Response
): SWError {
    switch (ErrorCode) {
        case ErrorCodes.TechnicalError:
            return new swErrors.GeneralError();
        case ErrorCodes.AuthenticationFailed:
            return new swErrors.AuthenticateFailedError();
        case ErrorCodes.InsufficientFunds:
            return new swErrors.InsufficientBalanceError();
        default:
            return new swErrors.GeneralError(ErrorMessage);
    }
}
```

## Configuration Management

### Environment-Based Configuration

Configuration is centralized and environment-driven:

```typescript
const config = {
    environment: process.env.NODE_ENV || "development",
    
    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 3000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 3001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 3002
    },
    
    db: {
        database: process.env.PGDATABASE || "management",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432
    },
    
    http: {
        operatorUrl: process.env.OPERATOR_HTTP_URL || "https://api.operator.com",
        defaultOptions: {
            timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 5000,
            proxy: process.env.OPERATOR_HTTP_PROXY
        }
    }
};
```

### Path Aliases

Module aliases improve code organization:

```typescript
// In package.json
"_moduleAliases": {
    "@entities": "src/entities",
    "@errors": "src/errors",
    "@utils": "src/utils",
    "@config": "src/config",
    "@wallet": "src/wallet",
    "@payment": "src/wallet/payment",
    "@launcher": "src/launcher",
    "@operator": "src/operator"
}
```

## Error Handling

### Error Hierarchy

The system uses a structured error hierarchy:

```typescript
// Base error class
export class SWError extends Error {
    constructor(
        public responseStatus: number,
        public code: number,
        message: string,
        public level?: ERROR_LEVEL,
        public extraData?: ExtraData,
        public responseData?: any
    ) {
        super(message);
    }
}

// Specific error types
export class ValidationError extends SWError {
    constructor(message = "") {
        super(400, 40, message, ERROR_LEVEL.WARN, {}, { messages: [message] });
    }
}

export class AuthenticateFailedError extends SWError {
    constructor(message = "Authentication failed") {
        super(401, 41, message, ERROR_LEVEL.WARN);
    }
}

export class InsufficientBalanceError extends SWError {
    constructor(message = "Insufficient balance") {
        super(400, 42, message, ERROR_LEVEL.WARN);
    }
}
```

### Error Handling Strategies

```typescript
// Retry conditions
export const retryCondition = (err: SWError): boolean =>
    err instanceof ConnectionError ||
    err instanceof TimeoutError ||
    err instanceof GeneralError;

// Rollback conditions
export const rollbackCondition = (err: SWError): boolean =>
    err instanceof ConnectionError ||
    err instanceof TimeoutError ||
    err instanceof GeneralError ||
    (err instanceof MerchantAdapterAPIError && err.code === DUPLICATE_KEY_ERROR_CODE);

// Error wrapping for specific contexts
export const wrapWinError = (err: SWError): SWError =>
    !(err instanceof MerchantInternalError)
        ? new MerchantInternalError(err.message, err.responseStatus)
        : err;
```

### Error Mapping Pattern

Systematic error mapping between operator and system errors:

```typescript
export function mapOperatorToSWError(
    { ErrorCode, ErrorMessage }: OperatorError,
    response?: superagent.Response
): SWError {
    switch (ErrorCode) {
        case ErrorCodes.TechnicalError:
            return new swErrors.GeneralError();
        case ErrorCodes.AuthenticationFailed:
            return new swErrors.AuthenticateFailedError();
        case ErrorCodes.InsufficientFunds:
            return new swErrors.InsufficientBalanceError();
        default:
            return new swErrors.GeneralError(ErrorMessage);
    }
}
```

### Error Interceptors

```typescript
@Injectable()
export class MockErrorInterceptor extends mock.CustomErrorInterceptor {
    getHandlerName(request: any, context: any): string {
        const originalAction = super.getHandlerName(request, context);
        return complexActions.includes(originalAction)
            ? this.getInnerAction(request.body, originalAction)
            : originalAction;
    }

    getInnerAction(request: OperatorTransactionRequest, prefix: string): string {
        return `${prefix}:${request.TransactionType.toLowerCase()}`;
    }
}
```

## Testing Strategy

### Unit Testing Framework

The system uses Mocha with TypeScript decorators:

```typescript
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { Test } from "@nestjs/testing";

@suite
class PaymentServiceSpec {
    private paymentService: PaymentService;
    private httpGateway: HttpGateway;

    public async before() {
        const module = await Test.createTestingModule({
            providers: [
                PaymentService,
                { provide: HttpGateway, useValue: mockHttpGateway },
                { provide: RoundService, useValue: mockRoundService }
            ]
        }).compile();

        this.paymentService = module.get<PaymentService>(PaymentService);
        this.httpGateway = module.get<HttpGateway>(HttpGateway);
    }

    @test
    public async "should process bet payment successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitBetPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }
}
```

### Mock System

Comprehensive mock system for testing:

```typescript
// Mock service base class
export abstract class NotSaveAnyDataMiddleware implements NestMiddleware {
    protected constructor(protected settingsService: SettingsService) {}

    public abstract async getResponse(req: any): Promise<any>;

    public async use(req: http.IncomingMessage, res: http.ServerResponse, next: () => void): Promise<any> {
        if (this.settingsService.loadTestModeEnable()) {
            res.setHeader(MOCK_RESPONSE_HEADER, "true");
            const body = await this.getBody(req);
            const responseData = await this.getResponse(req, body);
            return res.end(this.stringifyBody(responseData));
        } else {
            return next();
        }
    }
}

// Specific mock responder
@Injectable()
export class PaymentResponder extends NotSaveAnyDataMiddleware {
    public async getResponse(req: any): Promise<OperatorTransactionResponse> {
        return {
            Balance: { TotalAmount: this.settingsService.settings.amount },
            CurrencyCode: req.CurrencyCode || "EUR",
            ErrorDetails: null,
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString()
        };
    }
}
```

### Request Mocking

HTTP request mocking for integration tests:

```typescript
export function checkRequests(requestMock: testing.RequestMock, expectedRequests: ExpectedRequest[] = []) {
    expectedRequests.forEach((eRequest, i) => {
        const actualRequest = _.cloneDeep(requestMock.args[i]) as ExpectedRequest;
        eRequest.keysToVoid &&
            Object.keys(eRequest.keysToVoid).forEach((part) => {
                actualRequest[part] = voidObjectKeys(actualRequest[part], eRequest.keysToVoid[part]);
            });

        delete eRequest.keysToVoid;
        expect(actualRequest).deep.eq(eRequest);
    });
}

// Usage in tests
const mocker = testing.requestMock(superagent);
mocker.post("/api/transaction", testing.status200({ success: true }));

const result = await service.processPayment(request);
checkRequests(mocker, [expectedRequest]);
```

## Database Integration

### TypeORM Configuration

Database integration using TypeORM:

```typescript
@Module({
    imports: [
        TypeOrmModule.forRoot({
            type: "postgres",
            host: config.db.host,
            port: config.db.port,
            username: config.db.user,
            password: config.db.password,
            database: config.db.database,
            entities: [RoundEntity],
            synchronize: config.db.syncOnStart,
            logging: true,
            logger: config.db.logEnabled && new DbLogger(),
            cache: config.db.cache.isEnabled && { duration: config.db.cache.ttl },
            connectTimeoutMS: config.db.connectionTimeoutInMs
        })
    ]
})
export class DbModule {}
```

### Entity Design

```typescript
@Entity({ name: "rounds" })
export class RoundEntity {
    @Column({
        length: 100,
        nullable: false,
        name: "operator_round_id",
        primary: true
    })
    operatorRoundId: string;

    @Column({
        length: 100,
        nullable: false,
        name: "sw_round_id"
    })
    swRoundId: string;

    @Column({ name: "free_spin_token", nullable: true, length: 100 })
    freeSpinToken?: string;

    @CreateDateColumn({ name: "created_at" })
    createdAt: Date;

    @UpdateDateColumn({ name: "updated_at" })
    updatedAt: Date;

    public toInfo(): RoundDto {
        return {
            operatorRoundId: this.operatorRoundId,
            swRoundId: this.swRoundId,
            freeSpinToken: this.freeSpinToken
        };
    }
}
```

### Repository Pattern

```typescript
@Injectable()
export class RoundService {
    constructor(
        @InjectRepository(RoundEntity)
        private readonly roundRepository: Repository<RoundEntity>
    ) {}

    public async createRound(operatorRoundId: string, swRoundId: string): Promise<RoundEntity> {
        const round = this.roundRepository.create({
            operatorRoundId,
            swRoundId
        });
        return await this.roundRepository.save(round);
    }

    public async findByOperatorRoundId(operatorRoundId: string): Promise<RoundEntity | undefined> {
        return await this.roundRepository.findOne({
            where: { operatorRoundId }
        });
    }
}
```

## HTTP Communication

### HTTP Gateway Pattern

Centralized HTTP communication through gateway:

```typescript
@Injectable()
export class HttpGateway {
    constructor(private readonly httpService: HttpService) {}

    public async request<TRequest, TResponse>(
        request: TRequest,
        handler: HttpHandler<TRequest, TResponse>
    ): Promise<TResponse> {
        const httpRequest = await handler.build(request);
        const response = await this.executeRequest(httpRequest);
        return await handler.parse(response);
    }

    private async executeRequest(request: HTTPOperatorRequest): Promise<superagent.Response> {
        const agent = superagent.agent();

        // Configure SSL if needed
        if (request.ssl) {
            agent.ca(request.ssl.ca);
            agent.cert(request.ssl.cert);
            agent.key(request.ssl.key);
        }

        // Configure proxy if needed
        if (request.proxy) {
            agent.proxy(request.proxy);
        }

        const req = agent[request.method.toLowerCase()](request.url)
            .timeout(request.timeout)
            .set(request.headers);

        if (request.payload) {
            req.send(request.payload);
        }

        return await req;
    }
}
```

### Base HTTP Handler

```typescript
export abstract class BaseHttpHandler {
    protected buildBaseRequest(): OperatorBaseRequest {
        return {
            MessageID: generateNumber(config.messageIdNumberLength),
            UTCTimeStamp: new Date().toISOString()
        };
    }

    protected buildHttpRequest(request: HttpHandlerRequest): HTTPOperatorRequest {
        const url = this.buildUrl(request.path, request.jurisdiction);

        return {
            url,
            method: request.method,
            headers: this.buildHeaders(request.merchantInfo),
            payload: request.payload,
            timeout: config.http.defaultOptions.timeout,
            proxy: config.http.defaultOptions.proxy,
            ssl: config.http.ssl,
            retryAvailable: request.retryAvailable !== false
        };
    }

    protected parseHttpResponse<T>(response: superagent.Response): T {
        if (response.body.ErrorDetails) {
            throw mapOperatorToSWError(response.body.ErrorDetails, response);
        }
        return response.body as T;
    }

    private buildUrl(path: string, jurisdiction?: string): string {
        const baseUrl = config.http.operatorUrl;
        const versionPath = jurisdiction === OperatorJurisdiction.IT
            ? `/v${config.italyOperatorApiVersion}`
            : `/v${config.operatorApiVersion}`;

        return `${baseUrl}${versionPath}/${path}`;
    }

    private buildHeaders(merchantInfo: MerchantInfo): { [key: string]: string } {
        return {
            "Content-Type": "application/json",
            "User-Agent": `SW-Integration/${merchantInfo.code}`,
            "X-Merchant-Code": merchantInfo.code
        };
    }
}
```

## Deployment & Scaling

### Multi-Service Deployment

Each service can be deployed independently:

```typescript
// nest-cli.json configuration
{
    "collection": "@nestjs/schematics",
    "sourceRoot": "src",
    "projects": {
        "wallet": {
            "type": "application",
            "entryFile": "mainWallet"
        },
        "launcher": {
            "type": "application",
            "entryFile": "mainLauncher"
        },
        "operator": {
            "type": "application",
            "entryFile": "mainOperator"
        },
        "mock": {
            "type": "application",
            "entryFile": "mainMock"
        }
    }
}
```

### Build Scripts

```json
{
    "scripts": {
        "build": "npm run version && nest build",
        "start:launcher": "nest start launcher",
        "start:launcher:dev": "MEASURES_PROVIDER=prometheus nest start launcher --watch",
        "start:operator": "nest start operator",
        "start:operator:dev": "MEASURES_PROVIDER=prometheus nest start operator --watch",
        "start:wallet": "nest start wallet",
        "start:wallet:dev": "MEASURES_PROVIDER=prometheus nest start wallet --watch",
        "start:mock": "nest start mock"
    }
}
```

### Environment Configuration

```bash
# Server Configuration
SERVER_WALLET_PORT=3000
SERVER_LAUNCHER_PORT=3001
SERVER_OPERATOR_PORT=3002
SERVER_MOCK_PORT=3003
INTERNAL_SERVER_PORT=4054

# Database Configuration
PGDATABASE=management
PGUSER=postgres
PGPASSWORD=password
PGHOST=localhost
PGPORT=5432
PG_SECURE_CONNECTION=false
PG_MAX_CONNECTIONS=10

# Operator API Configuration
OPERATOR_HTTP_URL=https://api.operator.com
OPERATOR_HTTP_TIMEOUT=5000
OPERATOR_API_VERSION=3.0

# Logging Configuration
LOG_LEVEL=info
LOGGING_OUTPUT_TYPE=console
LOGGING_ROOT_LOGGER=sw-integration-api

# Security
OPERATOR_SSL_CA=/path/to/ca.pem
OPERATOR_SSL_CERT=/path/to/cert.pem
OPERATOR_SSL_KEY=/path/to/key.pem
```

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY lib/ ./lib/
COPY resources/ ./resources/

EXPOSE 3000 3001 3002 4054

CMD ["node", "lib/mainWallet.js"]
```

### Health Checks

```typescript
// Internal server for health checks
export async function createInternalServer(config: InternalServerConfig): Promise<INestApplication> {
    const app = await NestFactory.create(InternalModule);

    app.enableCors();
    app.use(helmet());

    await app.listen(config.port);

    return app;
}

// Health check endpoint
@Controller('health')
export class HealthController {
    @Get()
    getHealth(): { status: string; timestamp: string; service: string } {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: process.env.SERVICE_NAME || 'unknown'
        };
    }
}
```

## Best Practices & Guidelines

### 1. Code Organization

- **Modular Structure**: Organize code into logical modules with clear boundaries
- **Separation of Concerns**: Keep business logic separate from HTTP handling and data access
- **Dependency Injection**: Use NestJS DI container for loose coupling
- **Interface Segregation**: Define specific interfaces for different concerns

### 2. Error Handling

- **Structured Errors**: Use a consistent error hierarchy with proper error codes
- **Error Mapping**: Map external API errors to internal error types
- **Retry Logic**: Implement retry mechanisms for transient failures
- **Graceful Degradation**: Handle failures gracefully without breaking the entire flow

### 3. Testing

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Mock Services**: Use comprehensive mocking for external dependencies
- **Test Data**: Create reusable test data factories

### 4. Configuration

- **Environment-Based**: Use environment variables for configuration
- **Type Safety**: Define configuration interfaces with proper typing
- **Validation**: Validate configuration at startup
- **Secrets Management**: Keep sensitive data in secure configuration stores

### 5. Performance

- **Connection Pooling**: Use connection pools for database and HTTP connections
- **Caching**: Implement caching where appropriate
- **Monitoring**: Add performance monitoring and metrics
- **Resource Management**: Properly manage resources and connections

### 6. Security

- **Input Validation**: Validate all inputs using class-validator
- **Authentication**: Implement proper authentication mechanisms
- **Authorization**: Control access to resources
- **Secure Communication**: Use HTTPS and proper SSL configuration

### 7. Monitoring & Observability

- **Logging**: Implement structured logging with appropriate log levels
- **Metrics**: Collect business and technical metrics
- **Tracing**: Implement distributed tracing for request flows
- **Health Checks**: Provide health check endpoints for monitoring

## Implementation Checklist

When implementing a new integration system based on this architecture:

### Initial Setup
- [ ] Set up project structure with proper module organization
- [ ] Configure TypeScript with appropriate compiler options
- [ ] Set up NestJS with required dependencies
- [ ] Configure path aliases for clean imports
- [ ] Set up testing framework (Mocha/Jest)

### Core Components
- [ ] Implement bootstrap system for service startup
- [ ] Create base HTTP handler classes
- [ ] Implement HTTP gateway for centralized communication
- [ ] Set up error hierarchy and error mapping
- [ ] Create entity definitions with proper validation

### Services Implementation
- [ ] Implement wallet service for payment operations
- [ ] Implement launcher service for game URL generation
- [ ] Implement operator service for operator communications
- [ ] Create mock service for testing and development

### Database Integration
- [ ] Set up TypeORM configuration
- [ ] Create entity models with proper relationships
- [ ] Implement repository pattern for data access
- [ ] Set up database migrations

### Testing
- [ ] Create unit tests for all services
- [ ] Implement integration tests
- [ ] Set up mock system for external dependencies
- [ ] Create test data factories

### Configuration & Deployment
- [ ] Set up environment-based configuration
- [ ] Create Docker configuration
- [ ] Set up health check endpoints
- [ ] Configure logging and monitoring
- [ ] Set up CI/CD pipeline

### Documentation
- [ ] Document API endpoints
- [ ] Create deployment guides
- [ ] Document configuration options
- [ ] Create troubleshooting guides

This architecture guide provides a comprehensive foundation for building robust, scalable integration systems following established architectural patterns.
