{"compilerOptions": {"target": "es2022", "sourceMap": true, "outDir": "out", "module": "commonjs", "moduleResolution": "node", "isolatedModules": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": false, "noImplicitAny": false, "removeComments": true, "noLib": false, "preserveConstEnums": true, "inlineSources": false, "baseUrl": "./", "paths": {"@entities/*": ["src/entities/*"], "@errors/*": ["src/errors/*"], "@utils/*": ["src/utils/*"], "@config": ["src/config"], "@wallet/*": ["src/wallet/*"], "@launcher/*": ["src/launcher/*"], "@mock/*": ["src/mock/*"], "@names": ["src/names"]}, "skipLibCheck": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules"]}