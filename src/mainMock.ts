import { bootstrapMock } from "@skywind-group/sw-integration-core";
import { MockModule } from "./mock/mock.module";
import config from "./config";

async function bootstrap() {
    const app = await bootstrapMock({
        serviceName: "pronet-mock-server",
        versionFile: "./lib/version",
        module: MockModule,
        internalPort: config.internalServer.port,
        port: config.server.mockPort,
        actions: ["auth", "debit", "credit", "debit-credit", "rollback", "promo"],
        doNotStartApplication: true
    });
    await app.listen(config.server.mockPort, "0.0.0.0");
}

void bootstrap();
