import config from "@config";

export function sanitizeGameId(gameId: string): string {
    return gameId.trim().substring(0, 50);
}

export function formatCurrency(currency: string, demo: boolean): string {
    return demo ? "FUN" : currency.toUpperCase();
}

export function parsePlatformType(platform: string): string {
    switch (platform.toLowerCase()) {
        case "desktop":
        case "d":
            return "d";
        case "mobile":
        case "m":
            return "m";
        default:
            return "d";
    }
}

export function sumMajorUnits(...amounts: number[]): number {
    const total = amounts.reduce((sum, amount) => sum + (amount || 0), 0);
    return Math.round(total * config.currencyUnitMultiplier) / config.currencyUnitMultiplier;
}

export function buildGameUrl(baseUrl: string, params: any): string {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            queryParams.append(key, params[key].toString());
        }
    });
    return `${baseUrl}?${queryParams.toString()}`;
}

export function buildRoundDetailsUrl(baseUrl: string, params: any): string {
    return buildGameUrl(baseUrl, params);
}

export function generateTransactionId(type: string = "bet"): string {
    return `${type}_${Date.now()}_${generateNumber(6)}`;
}

export function generateBetId(roundId?: string): string {
    return `bet_${roundId}_${Date.now()}`;
}

export function isValidCurrency(currency: string): boolean {
    const validCurrencies = [
        "USD", "EUR", "GBP", "TRY", "CAD", "AUD", "JPY", "CHF", "SEK", "NOK", "DKK",
        "PLN", "CZK", "HUF", "RON", "BGN", "HRK", "RSD", "MKD", "ALL", "BAM",
        "FUN" // Demo currency
    ];
    
    return validCurrencies.includes(currency.toUpperCase());
}

export function convertToMajorUnits(amount: number, multiplier: number = 100): number {
    if (typeof amount !== "number" || isNaN(amount)) {
        return 0;
    }
    
    return Math.round(amount * multiplier) / multiplier;
}

export function convertToMinorUnits(amount: number): number {
    return Math.round(amount * config.currencyUnitMultiplier);
}

export function generateNumber(length: number): number {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function generateUniqueId(): string {
    return `${Date.now()}_${generateNumber(6)}`;
}

export function convertFromMinorUnits(amount: number): number {
    return amount / config.currencyUnitMultiplier;
}

export function isTokenExpired(tokenCreatedAt: Date): boolean {
    const now = new Date();
    const expirationTime = new Date(tokenCreatedAt.getTime() + (config.operator.tokenExpirationMinutes * 60 * 1000));
    return now > expirationTime;
}

export function extractOperatorError(response: any): { code: number; message: string } {
    if (response && response.code !== 0) {
        return {
            code: response.code,
            message: response.status || "Unknown error"
        };
    }
    return {
        code: -1,
        message: "Unknown error"
    };
}

export function sanitizeTableId(tableId?: string): string | null {
    if (!tableId || tableId.trim() === "") {
        return null;
    }
    return tableId.trim().substring(0, 50);
}

export function formatDateForOperator(date: Date): string {
    return date.toISOString();
}
