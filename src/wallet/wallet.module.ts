import { <PERSON>du<PERSON> } from "@nestjs/common";
import { PaymentModule, StartGameModule } from "@skywind-group/sw-integration-core";
import config from "@config";
import { PaymentService } from "@wallet/payment/payment.service";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { DebitCreditHttpHandler } from "@wallet/payment/debitCredit.http.handler";
import { RollbackHttpHandler } from "@wallet/payment/rollback.http.handler";
import { PromoHttpHandler } from "@wallet/payment/promo.http.handler";
import { GameTokenService } from "@wallet/start/gameToken.service";
import { GameUrlService } from "@wallet/start/gameUrl.service";

@Module({
    providers: [
        GameTokenService,
        GameUrlService,
        BetHttpH<PERSON><PERSON>,
        WinHttpHandler,
        DebitCreditHttpHandler,
        RollbackHttpHandler,
        PromoHttpHandler,
        BalanceHttpHandler,
        PaymentService
    ],
    exports: [
        GameTokenService,
        GameUrlService,
        BetHttpHandler,
        WinHttpHandler,
        DebitCreditHttpHandler,
        RollbackHttpHandler,
        PromoHttpHandler,
        BalanceHttpHandler,
        PaymentService
    ],
    imports: [
        StartGameModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                createGameToken: GameTokenService,
                createGameURL: GameUrlService
            },
            [WalletModule]
        ),
        PaymentModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                payment: PaymentService,
                jackpotPayment: PaymentService,
                bonusPayment: PaymentService
            },
            [WalletModule]
        )
    ]
})
export class WalletModule {}
