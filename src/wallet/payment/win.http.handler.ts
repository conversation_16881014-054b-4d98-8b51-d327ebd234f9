import {
    IntegrationGameTokenData, OperatorCreditRequest,
    OperatorCreditResponse
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { CommitPaymentRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler } from "@utils/baseHttp.handler";
import { sumMajorUnits } from "@utils/operator.utils";
import * as superagent from "superagent";

@Injectable()
export class WinHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {

    public async build({ request, gameTokenData, merchantInfo }: CommitPaymentRequest<IntegrationGameTokenData>): Promise<HTTPOperatorRequest> {
        return this.buildHttpRequest<OperatorCreditRequest>("credit", merchantInfo, {
            payload: {
                customer: gameTokenData.playerCode,
                token: gameTokenData.token,
                gameId: gameTokenData.gameCode,
                amount: this.sanitizeAmount(sumMajorUnits(request.totalWin)),
                currency: gameTokenData.currency,
                betId: this.generateBetId(request.roundPID),
                trxId: this.generateTransactionId("win"),
                freespin: this.buildFreespinInfo(this.extractFreespinData(request))
            },
            retryAvailable: true
        });
    }

    private extractFreespinData(paymentRequest: any): any {
        if (!paymentRequest.freeBetMode) {
            return undefined;
        }
        return {
            freespinRef: paymentRequest.externalId || paymentRequest.promoId,
            requested: paymentRequest.freeBetMode || false,
            remainingRounds: paymentRequest.freeBetBalance?.amount,
            totalWinnings: undefined
        };
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorCreditResponse>(response);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
