import {
    IntegrationGameTokenData,
    IntegrationPaymentRequest,
    OperatorDebitRequest,
    OperatorDebitResponse
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { CommitPaymentRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler } from "@utils/baseHttp.handler";
import { sumMajorUnits } from "@utils/operator.utils";
import * as superagent from "superagent";

@Injectable()
export class BetHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {

    public async build({ request, gameTokenData, merchantInfo }: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        return this.buildHttpRequest<OperatorDebitRequest>("debit", merchantInfo, {
            payload: {
                customer: gameTokenData.playerCode,
                token: gameTokenData.token,
                gameId: gameTokenData.gameCode,
                amount: this.sanitizeAmount(sumMajorUnits(request.bet)),
                currency: gameTokenData.currency,
                betId: this.generateBetId(request.roundPID),
                trxId: this.generateTransactionId("bet"),
                tip: false // Operator supports tip parameter for some games
            },
            retryAvailable: true
        });
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorDebitResponse>(response);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
