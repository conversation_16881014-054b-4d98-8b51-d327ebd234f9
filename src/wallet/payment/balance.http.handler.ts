import { IntegrationGameTokenData, OperatorAuthRequest, OperatorAuthResponse } from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { BalanceRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler } from "@utils/baseHttp.handler";
import * as superagent from "superagent";

export type IntegrationGetBalanceRequest = BalanceRequest<IntegrationGameTokenData>;

@Injectable()
export class BalanceHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationGetBalanceRequest, Balance> {

    public async build({ gameTokenData: { playerCode: customer, token }, merchantInfo }: IntegrationGetBalanceRequest): Promise<HTTPOperatorRequest> {
        return this.buildHttpRequest<OperatorAuthRequest>("auth", merchantInfo, {
            payload: { customer, token },
            retryAvailable: true
        });
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const { balance, bonusBalance } = this.parseHttpResponse<OperatorAuthResponse>(response);
        return {
            main: this.sanitizeAmount(balance + (bonusBalance || 0))
        };
    }
}
