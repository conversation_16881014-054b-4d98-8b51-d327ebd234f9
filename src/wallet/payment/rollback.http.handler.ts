import {
    IntegrationPaymentRequest, OperatorRollbackRequest,
    OperatorRollbackResponse
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { <PERSON>ttp<PERSON>and<PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler } from "@utils/baseHttp.handler";
import * as superagent from "superagent";

@Injectable()
export class RollbackHttpHandler extends BaseHttpHandler
    implements HttpHandler<IntegrationPaymentRequest, Balance> {

    public async build({ request, gameTokenData, merchantInfo }: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        // For rollback, we need the original transaction ID
        return this.buildHttpRequest<OperatorRollbackRequest>("rollback", merchantInfo, {
            payload: {
                customer: gameTokenData.playerCode,
                token: gameTokenData.token,
                gameId: gameTokenData.gameCode,
                trxId: request.transactionId.publicId
            },
            retryAvailable: true
        });
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorRollbackResponse>(response);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
