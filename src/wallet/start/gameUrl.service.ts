import { IntegrationInitRequest, IntegrationMerchantGameURLInfo } from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { CreateGameUrlRequest, CreateGameUrlSupport } from "@skywind-group/sw-integration-core";
import { measures } from "@skywind-group/sw-utils";

@Injectable()
export class GameUrlService implements CreateGameUrlSupport<IntegrationInitRequest> {

    @measures.measure({ name: "GameUrlService.createGameUrl", isAsync: true })
    public async createGameUrl(req: CreateGameUrlRequest<IntegrationInitRequest>): Promise<IntegrationMerchantGameURLInfo> {
        return {
            urlParams: {},
            tokenData: this.createTokenData(req)
        };
    }

    private createTokenData(req: CreateGameUrlRequest<IntegrationInitRequest>): IntegrationMerchantGameURLInfo["tokenData"] {
        if (req.initRequest.previousStartTokenData) {
            return {
                ...req.initRequest.previousStartTokenData,
                gameCode: req.gameCode,
                providerCode: req.providerCode,
                providerGameCode: req.providerGameCode
            };
        } else {
            return {
                merchantType: req.merchantInfo.type,
                merchantCode: req.merchantInfo.code,
                brandId: req.merchantInfo.brandId,
                providerCode: req.providerCode,

                gameCode: req.gameCode,
                providerGameCode: req.providerGameCode,

                playerCode: req.initRequest.customer,
                currency: req.initRequest.currency,
                country: req.initRequest.country,
                language: req.initRequest.language,

                token: req.initRequest.ticket,
            };
        }
    }
}
