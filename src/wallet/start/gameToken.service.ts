import { IntegrationGameTokenData, IntegrationStartGameTokenData } from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { CreateGameTokenRequest, CreateGameTokenSupport } from "@skywind-group/sw-integration-core";
import { measures } from "@skywind-group/sw-utils";

@Injectable()
export class GameTokenService implements CreateGameTokenSupport<IntegrationStartGameTokenData, IntegrationGameTokenData> {

    @measures.measure({ name: "GameTokenService.createGameTokenData", isAsync: true })
    public async createGameTokenData(req: CreateGameTokenRequest<IntegrationStartGameTokenData>) {
        const gameTokenData: IntegrationGameTokenData = {
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            brandId: req.startGameToken.brandId,
            playerCode: req.startGameToken.playerCode,
            gameCode: req.startGameToken.gameCode,
            playmode: req.startGameToken.playmode,
            currency: req.currency,
            country: req.startGameToken.country,
            
            test: req.startGameToken.test,
            transferEnabled: req.transferEnabled,
            isPromoInternal: req.merchantInfo.params.isPromoInternal || false,

            token: req.startGameToken.token,
        };
        return {
            gameTokenData
        };
    }
}
