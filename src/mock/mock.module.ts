import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { MockController } from "./mock.controller";
import { MockService } from "./mock.service";
import { AuthResponder } from "./responders/auth.responder";
import { DebitCreditResponder } from "./responders/debit-credit.responder";
import { PaymentResponder } from "./responders/payment.responder";
import { IdentityGuard } from "./identity.guard";

@Module({
    controllers: [MockController],
    providers: [
        MockService,
        IdentityGuard,
        AuthResponder,
        PaymentResponder,
        DebitCreditResponder
    ],
    imports: [mock.MockModule]
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        consumer
            .apply(AuthResponder).forRoutes("/casino-engine/generic/:vendorCode/v5/auth")
            .apply(PaymentResponder).forRoutes("/casino-engine/generic/:vendorCode/v5/debit")
            .apply(PaymentResponder).forRoutes("/casino-engine/generic/:vendorCode/v5/credit")
            .apply(DebitCreditResponder).forRoutes("/casino-engine/generic/:vendorCode/v5/debit-credit")
            .apply(PaymentResponder).forRoutes("/casino-engine/generic/:vendorCode/v5/rollback")
            .apply(PaymentResponder).forRoutes("/casino-engine/generic/:vendorCode/v5/promo");
    }
}
