import { OperatorBaseRequest } from "@entities/operator.entities";
import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Injectable()
export class IdentityGuard implements CanActivate {
    constructor(private readonly ticketService: mock.TicketService, private readonly merchantService: mock.MerchantService) { }

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();

        const ticket = (request.body as OperatorBaseRequest).token;
        const { merchantId, customerId } = this.ticketService.getById(ticket);
        const merchant = this.merchantService.getById(merchantId);
        if (!merchant) {
            throw new mock.errors.GameTokenExpired(`Merchant [${merchantId}] from ticket [${ticket}] not found`);
        }
        const customer = this.merchantService.getCustomerById(merchantId, customerId);
        if (!customer) {
            throw new mock.errors.GameTokenExpired(`Customer [${customerId}] from ticket [${ticket}] not found`);
        }

        request.merchant = merchant;
        request.customer = customer;
        return true;
    }
}
