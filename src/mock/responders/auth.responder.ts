import { OperatorAuthRequest, OperatorAuthResponse } from "@entities/operator.entities";
import { getOperatorResponse } from "@mock/utils/response-builder";
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import * as http from "http";

@Injectable()
export class AuthResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService, private readonly ticketService: mock.TicketService) {
        super(settingsService);
    }

    public async getResponse(_req: http.IncomingMessage, { token }: OperatorAuthRequest): Promise<OperatorAuthResponse> {
        const [, currency] = this.ticketService.getDataFromTicket(token);
        return {
            ...getOperatorResponse(this.settingsService.settings.amount, currency),
            traderId: 0
        };
    }
}
