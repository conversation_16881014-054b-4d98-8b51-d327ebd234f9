import { OperatorDebitCreditRequest, OperatorDebitCreditResponse } from "@entities/operator.entities";
import { getOperatorPaymentResponse } from "@mock/utils/response-builder";
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import * as http from "http";

@Injectable()
export class DebitCreditResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService, private readonly ticketService: mock.TicketService) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, { token }: OperatorDebitCreditRequest): Promise<OperatorDebitCreditResponse> {
        const [, currency] = this.ticketService.getDataFromTicket(token);
        return {
            ...getOperatorPaymentResponse(this.settingsService.settings.amount, currency),
            creditTrxId: 0
        };
    }
}
