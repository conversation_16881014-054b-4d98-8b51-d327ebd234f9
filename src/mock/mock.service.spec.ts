import { Test, TestingModule } from "@nestjs/testing";
import { mock } from "@skywind-group/sw-integration-core";
import { expect } from "chai";
import { suite, test } from "mocha-typescript";
import "module-alias/register";
import { MockService } from "./mock.service";

@suite
class MockServiceTest {
    private service: MockService;
    private mockMerchant: mock.Merchant;
    private mockCustomer: mock.Customer;

    async before() {
        // Create mock merchant and customer objects
        this.mockMerchant = {
            merch_id: "test_merchant",
            merch_name: "Test Merchant",
            merch_pwd: "password",
            merch_url: "http://test.com",
            currency_code: "USD"
        } as mock.Merchant;

        this.mockCustomer = {
            cust_id: "2019045569026",
            cust_login: "test_customer",
            currency_code: "USD",
            status: "normal",
            bet_limit: null,
            test_cust: true,
            country: "US",
            language: "en",
            balance: {
                amount: 1000,
                currency_code: "USD"
            }
        } as mock.Customer;

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                MockService,
                {
                    provide: mock.CustomerService,
                    useValue: {
                        getMerchantCustomer: () => this.mockCustomer
                    }
                },
                {
                    provide: mock.SessionService,
                    useValue: {
                        generate: () => ({}),
                        generateWithId: () => ({})
                    }
                },
                {
                    provide: mock.SettingsService,
                    useValue: {
                        settings: { amount: 100 }
                    }
                },
                {
                    provide: mock.TicketService,
                    useValue: {
                        getDataFromTicket: () => ["2019045569026", "USD"]
                    }
                },
                {
                    provide: mock.TransactionService,
                    useValue: {
                        getById: () => null,
                        create: (_merchantId: string, _customerId: string, data: any) => ({
                            trxId: "test_trx",
                            amount: data.action === "debit" ? -Math.abs(data.amount) : Math.abs(data.amount),
                            action: data.action
                        }),
                        rollback: () => ({})
                    }
                }
            ]
        }).compile();

        this.service = module.get<MockService>(MockService);
    }

    beforeEach() {
        // Reset customer balance before each test to ensure test isolation
        this.mockCustomer.balance.amount = 1000;
    }

    private createFreshCustomer(overrides: Partial<mock.Customer> = {}): mock.Customer {
        return {
            cust_id: "2019045569026",
            cust_login: "test_customer",
            currency_code: "USD",
            status: "normal",
            bet_limit: null,
            test_cust: true,
            country: "US",
            language: "en",
            balance: {
                amount: 1000,
                currency_code: "USD"
            },
            ...overrides
        } as mock.Customer;
    }

    @test
    "should be defined"() {
        expect(this.service).to.exist;
    }

    @test
    async "should return customer balance and trader ID for auth"() {
        const response = this.service.getBalance(this.mockMerchant, this.mockCustomer);

        expect(response).to.deep.equal({
            balance: 1000,
            bonusBalance: 0,
            code: 0,
            currency: "USD",
            status: "Success"
        });
    }

    @test
    async "should return customer balance for suspended customer"() {
        // Create a suspended customer
        const suspendedCustomer = {
            ...this.mockCustomer,
            cust_id: "2021030100001",
            status: "suspended"
        } as mock.Customer;

        const response = this.service.getBalance(this.mockMerchant, suspendedCustomer);

        expect(response.balance).to.equal(1000);
        expect(response.currency).to.equal("USD");
        expect(response.status).to.equal("Success");
    }

    @test
    async "should debit customer balance successfully"() {
        const customer = this.createFreshCustomer();
        const response = this.service.debit(this.mockMerchant, customer, {
            customer: customer.cust_id,
            token: "valid_token",
            gameId: "test_game",
            amount: 10,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456"
        });

        expect(response.balance).to.equal(990); // 1000 - 10
        expect(response.currency).to.equal("USD");
        expect(response.trxId).to.equal("test_trx");
    }

    @test
    async "should handle debit with large amount"() {
        // Create customer with low balance
        const lowBalanceCustomer = this.createFreshCustomer({
            balance: {
                amount: 10,
                currency_code: "USD"
            }
        });

        // This should throw an InsufficientBalance error
        expect(() => {
            this.service.debit(this.mockMerchant, lowBalanceCustomer, {
                customer: lowBalanceCustomer.cust_id,
                token: "valid_token",
                gameId: "test_game",
                amount: 2000, // More than available balance
                currency: "USD",
                betId: "bet_123",
                trxId: "trx_456"
            });
        }).to.throw("Insufficient balance");
    }

    @test
    async "should credit customer balance after debit"() {
        const customer = this.createFreshCustomer();

        // First place a bet
        this.service.debit(this.mockMerchant, customer, {
            customer: customer.cust_id,
            token: "valid_token",
            gameId: "test_game",
            amount: 10,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456"
        });

        // Then settle the win
        const response = this.service.credit(this.mockMerchant, customer, {
            customer: customer.cust_id,
            token: "valid_token",
            gameId: "test_game",
            amount: 25,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_789"
        });

        expect(response.balance).to.equal(1015); // 1000 - 10 + 25
        expect(response.currency).to.equal("USD");
        expect(response.trxId).to.equal("test_trx");
    }

    @test
    async "should credit customer balance"() {
        const customer = this.createFreshCustomer();
        const response = this.service.credit(this.mockMerchant, customer, {
            customer: customer.cust_id,
            token: "valid_token",
            gameId: "test_game",
            amount: 25,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_789"
        });

        expect(response.balance).to.equal(1025); // 1000 + 25
        expect(response.currency).to.equal("USD");
        expect(response.trxId).to.equal("test_trx");
    }

    @test
    async "should process debit and credit in one transaction"() {
        const customer = this.createFreshCustomer();
        const response = this.service.debitCredit(this.mockMerchant, customer, {
            customer: customer.cust_id,
            token: "valid_token",
            gameId: "test_game",
            amount: 10,
            creditAmount: 25,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456",
            creditTrxId: "trx_789"
        });

        expect(response.balance).to.equal(1015); // 1000 - 10 + 25
        expect(response.currency).to.equal("USD");
    }

    @test
    async "should process freespin promotional win"() {
        const response = this.service.promo(this.mockCustomer);

        expect(response.balance).to.equal(1000);
        expect(response.currency).to.equal("USD");
        expect(response.bonusBalance).to.equal(0);
    }
}
