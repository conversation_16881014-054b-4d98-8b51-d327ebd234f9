import {
    OperatorAuthResponse,
    OperatorCreditRequest,
    OperatorCreditResponse,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse,
    OperatorDebitRequest,
    OperatorDebitResponse,
    OperatorPaymentRequest,
    OperatorPaymentResponse,
    OperatorPromoResponse,
    OperatorRollbackRequest
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { getOperatorPaymentResponse, getOperatorResponse } from "./utils/response-builder";

@Injectable()
export class MockService extends mock.MockService {

    constructor(
        protected readonly customerService: mock.CustomerService,
        protected readonly sessionService: mock.SessionService,
        protected readonly ticketService: mock.TicketService,
        protected readonly transactionService: mock.TransactionService
    ) {
        super(customerService, sessionService, ticketService, transactionService);
    }

    public getBalance(_merchant: mock.Merchant, customer: mock.Customer): OperatorAuthResponse {
        return getOperatorResponse(customer.balance.amount, customer.balance.currency_code);
    }

    public debit(merchant: mock.Merchant, customer: mock.Customer, request: OperatorDebitRequest): OperatorDebitResponse {
        return super.debit(merchant, customer, request);
    }

    public credit(merchant: mock.Merchant, customer: mock.Customer, request: OperatorCreditRequest): OperatorCreditResponse {
        return super.credit(merchant, customer, request);
    }

    public debitCredit(merchant: mock.Merchant, customer: mock.Customer, request: OperatorDebitCreditRequest): OperatorDebitCreditResponse {
        this.debit(merchant, customer, request);
        this.credit(merchant, customer, {
            ...request,
            amount: request.creditAmount,
            trxId: request.creditTrxId
        });
        return getOperatorPaymentResponse(customer.balance.amount, customer.balance.currency_code);
    }

    public promo(customer: mock.Customer): OperatorPromoResponse {
        return getOperatorPaymentResponse(customer.balance.amount, customer.balance.currency_code);
    }

    // used in authenticate()
    protected getTicketId() {
        return "";
    }

    // used in authenticate()
    protected getAuthResponse() {
        return {};
    }

    // used in makePayment()
    protected getAmount(body: OperatorPaymentRequest): number {
        return body.amount;
    }

    // used in makePayment() & rollback()
    protected getTransactionId(body: OperatorPaymentRequest | OperatorRollbackRequest): string {
        return body.trxId;
    }

    // used in makePayment()
    protected isFreebet(body: OperatorCreditRequest | OperatorDebitRequest, action: mock.WalletAction): boolean {
        if (action === mock.WalletAction.Credit) {
            return (body as OperatorCreditRequest).freespin !== undefined;
        }
        return false;
    }

    // used in makePayment()
    protected getPaymentResponse(_merchant: mock.Merchant, customer: mock.Customer, transaction: mock.Transaction): OperatorPaymentResponse {
        return getOperatorPaymentResponse(customer.balance.amount, customer.balance.currency_code, transaction.trxId);
    }

    protected getDebitTransactionIdInRollbackRequest(body: OperatorRollbackRequest): string {
        return body.trxId;
    }
}
