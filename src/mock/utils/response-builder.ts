import { OperatorBaseResponse, OperatorPaymentResponse } from "@entities/operator.entities";

export function getOperatorResponse(amount: number, currency: string): OperatorBaseResponse {
    return {
        balance: amount,
        bonusBalance: 0,
        code: 0,
        currency,
        status: "Success"
    };
}

export function getOperatorPaymentResponse(amount: number, currency: string, trxId = "0"): OperatorPaymentResponse {
    return {
        ...getOperatorResponse(amount, currency),
        trxId
    };
}
