FROM node:22.16.0-alpine as build

WORKDIR /app
COPY . /app/

RUN apk add --no-cache git && apk cache clean
ENV COREPACK_INTEGRITY_KEYS=0
ENV CI=true
RUN corepack enable && \
    pnpm install --no-frozen-lockfile && \
    pnpm run lint && \
    pnpm run clean && \
    pnpm run compile && \
    pnpm run version

FROM node:22.14.0-alpine
WORKDIR /app

COPY --chown=node:node --from=build /app/package.json /app/pnpm-lock.yaml /app/preinstall.cjs /app/.npmrc ./
COPY --chown=node:node --from=build /app/out ./out

ENV COREPACK_INTEGRITY_KEYS=0
ENV CI=true
RUN corepack enable \
    && pnpm install --frozen-lockfile --prod \
    && pnpm store prune \
    && rm .npmrc \
    && rm -fr /root/.cache \
    && corepack disable

USER node
CMD ["node", "/app/out/mainLauncher"]
