"use strict";
/** @type {import('eslint').Linter.Config[]} */
const typescriptEslint = require("@typescript-eslint/eslint-plugin");
const typescriptParser = require("@typescript-eslint/parser");

module.exports = [
    {
        ignores: [
            "out/*",
            "out/**/*",
            "eslint.config.js",
            "node_modules/*",
            "node_modules/**/*",
        ],
        files: ["**/*.ts"],
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: "module",
            parser: typescriptParser,
            parserOptions: {
                tsconfigRootDir: __dirname,
                project: ["./tsconfig.json"],
            },
        },
        plugins: {
            "@typescript-eslint": typescriptEslint,
        },
        rules: {
            "semi": ["error"],
            "arrow-spacing": "error",
            "no-console": ["error"],
            "quotes": ["error", "double"],
            "indent": ["error", 4, {
                "SwitchCase": 1,
                "ignoredNodes": ["PropertyDefinition[decorators.length > 0]"]
            }],
            "function-call-argument-newline": ["error", "consistent"],
            "function-paren-newline": ["error", "consistent"]
        }
    },
]
